package com.altomni.apn.common.example;

import com.altomni.apn.common.utils.MDCUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * MDC 异步使用示例
 * 展示如何在异步任务中保持 traceId
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class MDCAsyncExample {
    
    @Resource
    @Qualifier("mdcTaskExecutor")
    private Executor mdcTaskExecutor;
    
    /**
     * 示例1：使用 MDCUtils.runAsync 方法
     */
    public void example1() {
        // 设置当前线程的 traceId
        MDC.put("traceId", UUID.randomUUID().toString());
        log.info("主线程开始执行，traceId: {}", MDC.get("traceId"));
        
        // 使用 MDCUtils.runAsync，会自动传递 MDC 上下文
        MDCUtils.runAsync(() -> {
            log.info("异步任务执行中，traceId: {}", MDC.get("traceId"));
            // 这里的 traceId 应该和主线程的一致
        });
        
        log.info("主线程继续执行");
    }
    
    /**
     * 示例2：使用配置的 MDC 线程池
     */
    public void example2() {
        // 设置当前线程的 traceId
        MDC.put("traceId", UUID.randomUUID().toString());
        log.info("主线程开始执行，traceId: {}", MDC.get("traceId"));
        
        // 使用配置的 MDC 线程池，会自动传递 MDC 上下文
        mdcTaskExecutor.execute(() -> {
            log.info("异步任务执行中，traceId: {}", MDC.get("traceId"));
            // 这里的 traceId 应该和主线程的一致
        });
        
        log.info("主线程继续执行");
    }
    
    /**
     * 示例3：使用 CompletableFuture 链式调用
     */
    public void example3() {
        // 设置当前线程的 traceId
        MDC.put("traceId", UUID.randomUUID().toString());
        log.info("主线程开始执行，traceId: {}", MDC.get("traceId"));
        
        // 使用 MDCUtils.supplyAsync 和链式调用
        MDCUtils.supplyAsync(() -> {
            log.info("第一个异步任务，traceId: {}", MDC.get("traceId"));
            return "第一个任务结果";
        }).thenApply(MDCUtils.wrapFunction(result -> {
            log.info("第二个异步任务，traceId: {}, 上一个结果: {}", MDC.get("traceId"), result);
            return result + " -> 第二个任务结果";
        })).thenAccept(finalResult -> {
            log.info("最终结果: {}, traceId: {}", finalResult, MDC.get("traceId"));
        });
        
        log.info("主线程继续执行");
    }
    
    /**
     * 示例4：手动包装 Runnable
     */
    public void example4() {
        // 设置当前线程的 traceId
        MDC.put("traceId", UUID.randomUUID().toString());
        log.info("主线程开始执行，traceId: {}", MDC.get("traceId"));
        
        // 手动包装 Runnable
        Runnable task = () -> {
            log.info("异步任务执行中，traceId: {}", MDC.get("traceId"));
        };
        
        // 使用普通的 CompletableFuture，但包装 Runnable
        CompletableFuture.runAsync(MDCUtils.wrapRunnable(task));
        
        log.info("主线程继续执行");
    }
}
