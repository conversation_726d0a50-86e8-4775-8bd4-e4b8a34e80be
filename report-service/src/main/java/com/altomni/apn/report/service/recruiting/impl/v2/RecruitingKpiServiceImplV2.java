package com.altomni.apn.report.service.recruiting.impl.v2;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.StageKpiReportDto;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.utils.FutureExceptionUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiJobDetailCommonCountVO;
import com.altomni.apn.common.vo.recruiting.v2.KpiReportCreatedVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiNoteCountVO;
import com.altomni.apn.common.vo.recruiting.v2.ReserveInterviewVO;
import com.altomni.apn.report.domain.vo.TeamIdName;
import com.altomni.apn.report.repository.RecruitingKpiUserRepository;
import com.altomni.apn.report.repository.v2.RecruitingKpiCompanyRepositoryV2;
import com.altomni.apn.report.repository.v2.RecruitingKpiUserRepositoryV2;
import com.altomni.apn.report.service.recruiting.RecruitingKpiService;
import com.altomni.apn.report.service.recruiting.impl.RecruitingKpiBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("recruitingKpiServiceV2")
public class RecruitingKpiServiceImplV2 extends RecruitingKpiBaseServiceImpl implements RecruitingKpiService {

    @Resource
    private RecruitingKpiUserRepositoryV2 recruitingKpiUserRepositoryV2;

    @Resource
    private RecruitingKpiCompanyRepositoryV2 recruitingKpiCompanyRepositoryV2;

    /**
     * 查询用户的 KPI 数据，仅在以下三种情况会真正查询数据
     * 1. 用户是个人数据权限，只查询个人数据
     * 2. 有用户搜索时，按照用户维度查询搜索的用户的数据
     * 3. 部门下钻时，查询部门下用户的数据
     */
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUserV2(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        List<Long> userIdList = new ArrayList<>(searchDto.getUserIdList());
        if (Boolean.TRUE.equals(teamDTO.getSelf())) {
           userIdList = Collections.singletonList(SecurityUtils.getUserId());
        }
        if (CollectionUtils.isEmpty(userIdList) && Objects.isNull(searchDto.getParentId())) {
            log.info("[apn @{}] search recruiting kpi report by user, userIdList = {}, parent = null, return empty list", SecurityUtils.getUserId(), userIdList);
            return Collections.emptyList();
        }

        RecruitingKpiReportSearchDto search = searchDto.deepCopy();
        search.setUserIdList(userIdList);
        search.setTeamIdList(Collections.emptyList());
        Set<RecruitingKpiGroupByFieldType> dims = Stream.concat(Stream.of(RecruitingKpiGroupByFieldType.USER), RecruitingKpiGroupByFieldType.ALL_TIME_LIST.stream()).collect(Collectors.toSet());
        List<RecruitingKpiGroupByFieldType> groupByField = search.getGroupByFieldList().stream().filter(dims::contains).toList();
        search.setGroupByFieldList(groupByField.isEmpty() ? List.of(RecruitingKpiGroupByFieldType.USER) : groupByField);
        List<RecruitingKpiByUserVO> voList = searchRecruitingKpiReportByUser(search, teamDTO);
        List<RecruitingKpiByUserVO> resultVoList = new ArrayList<>();
        List<Long> userIds = voList.stream().map(RecruitingKpiCommonCountVO::getUserId).filter(Objects::nonNull).toList();
        Map<Long, TeamInfoVO> userTeamMap = userService.getTeamInfoVOsByUserIds(userIds).getBody()
                .stream().collect(Collectors.toMap(TeamInfoVO::getUserId, Function.identity()));
        List<StageKpiReportDto> stageSearchFilter = addStageFilter(search);
        voList.forEach(vo -> {
            if (checkSearchFilter(vo, stageSearchFilter, search.getApplicationStatusType(), search.getStayedOverList())) {
                resultVoList.add(vo);
            }
            vo.setParentId(userTeamMap.getOrDefault(vo.getUserId(), new TeamInfoVO()).getTeamId());
        });
        return resultVoList;
    }

    /**
     * 以团队为维度，查询团队的 KPI 数据，每个团队会包含其下属所有子团队的数据
     * 1. 个人数据权限。返回空列表
     * 2. 有团队搜索，按照搜索的团队查询
     * 3. 无团队搜索，查询所有团队的数据
     * 4. 有用户搜索，并且不是个人数据权限，搜索用户所在团队的数据
     */
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByTeam(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        // 个人权限时，不允许查询团队数据
        if (Boolean.TRUE.equals(teamDTO.getSelf())) {
            return Collections.emptyList();
        }
        RecruitingKpiReportSearchDto search = searchDto.deepCopy();
        List<Long> userIdList = searchDto.getUserIdList();
        if (!CollectionUtils.isEmpty(userIdList)) {
            List<TeamIdName> teamIds = reportRepository.findTeamsByUserIdsAndTeamIds(userIdList, searchDto.getTeamIdList(), SecurityUtils.getTenantId());
            if (!CollectionUtils.isEmpty(teamIds)) {
                search.setTeamIdList(teamIds.stream().map(TeamIdName::getId).toList());
            }
        }
        Set<RecruitingKpiGroupByFieldType> dims = Stream.concat(Stream.of(RecruitingKpiGroupByFieldType.TEAM), RecruitingKpiGroupByFieldType.ALL_TIME_LIST.stream()).collect(Collectors.toSet());
        List<RecruitingKpiGroupByFieldType> groupByField = search.getGroupByFieldList().stream().filter(dims::contains).toList();
        search.setGroupByFieldList(groupByField.isEmpty() ? List.of(RecruitingKpiGroupByFieldType.TEAM) : groupByField);
        search.setUserIdList(Collections.emptyList());
        return searchRecruitingKpiReportByUser(search, teamDTO);
    }



    @Override
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        List<RecruitingKpiByUserVO> teamData = searchRecruitingKpiReportByTeam(searchDto, teamDTO);
        List<RecruitingKpiByUserVO> userData = searchRecruitingKpiReportByUserV2(searchDto, teamDTO);
        return Stream.of(teamData, userData).flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public RecruitingKpiByUserVO searchRecruitingKpiTotalByUser(RecruitingKpiReportSearchDto searchDto) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TENANT));
        return searchRecruitingKpiReportByUser(searchDto, teamDTO).stream().findFirst().orElse(null);
    }

    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        log.info("[apn @{}] search recruiting kpi report by user, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("recruiting kpi report by user");

        stopWatch.start("[2] search job、talent、application、note、company info、crm company info data task");
        Function<RecruitingKpiCommonCountVO, String> function = getMapKey(searchDto.getGroupByFieldList());
        boolean skipQuery = searchDto.isXxlJobFlag() || searchDto.isE5ReportFlag();

        var applicationFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepositoryV2.searchApplicationKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), starRocksExecutor);

        var reserveInterviewFuture = CompletableFuture.supplyAsync(() -> {
            if (RecruitingKpiDateType.ADD.equals(searchDto.getDateType())) {
                return Collections.<String, ReserveInterviewVO>emptyMap();
            }
            return recruitingKpiUserRepositoryV2.searchReserveInterviewKpiData(searchDto, teamDTO)
                    .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        var noteFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiNoteCountVO> voList = searchDto.isXxlJobFlag() ? List.of() : recruitingKpiUserRepositoryV2.searchNoteKpiData(searchDto, teamDTO);
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        var createdDataFuture = CompletableFuture.supplyAsync(() -> {
            List<KpiReportCreatedVO> voList = skipQuery ? List.of() : recruitingKpiUserRepositoryV2.searchCreatedKpiData(searchDto, teamDTO);
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);


        CompletableFuture.allOf(applicationFuture, noteFuture, createdDataFuture).exceptionally(FutureExceptionUtil::handleFutureException);

        var applicationMap = applicationFuture.join();
        var reserveInterviewMap = reserveInterviewFuture.join();
        var noteMap = noteFuture.join();
        var createdDataMap = createdDataFuture.join();
        stopWatch.stop();

        Set<String> keySet = Stream.of(applicationMap, reserveInterviewMap, noteMap, createdDataMap).parallel()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        stopWatch.start("[4] assemble data task");

        CopyOnWriteArrayList<RecruitingKpiByUserVO> voList = new CopyOnWriteArrayList<>();
        keySet.parallelStream().forEach(key -> {
            try {
                RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                setGroupByField(vo, key, searchDto.getGroupByFieldList());
                setVoNumV2(vo, applicationMap, reserveInterviewMap, noteMap, createdDataMap, key, searchDto);
                voList.add(vo);
            } catch (Exception e) {
                log.error(" search kpi by user assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                throw e;
            }
        });
        stopWatch.stop();

        List<RecruitingKpiGroupByFieldType> groupByFieldList = searchDto.getGroupByFieldList();
        if (BooleanUtil.isFalse(CollectionUtils.isEmpty(groupByFieldList))) {
            this.appendEmptyData(searchDto.getUser(), groupByFieldList, voList, searchDto.getTeamIdList(), searchDto.getUserIdList(), teamDTO);
        }
        stopWatch.start("[4] search filter data task");
        stopWatch.stop();
        log.info("[apn @{}] searchRecruitingKpiReportByUser time = {}ms \n {}", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());

        return sortVoListByFields(voList, groupByFieldList);
    }


    @Override
    public List<RecruitingKpiByCompanyVO> searchRecruitingKpiReportByCompany(RecruitingKpiReportSearchDto searchDto) {

        log.info("[apn @{}] search recruiting kpi report by company v2, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("recruiting kpi report by company v2");
        stopWatch.start("[1] search company permission data task");
        //init report permission
        getPermissionDTOAndSetCommonParam(searchDto);
        // if group by company, ignore user active status filter
        stopWatch.stop();
        stopWatch.start("[2] search company data task");

        Function<RecruitingKpiCommonCountVO, String> function = getMapKey(searchDto.getGroupByFieldList());

        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);

        //job
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> jobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepositoryV2.searchJobKpiByCompany(searchDto,jobDetailFlag), starRocksExecutor);
        CompletableFuture<List<RecruitingKpiCommonCountVO>> talentFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepositoryV2.searchTalentKpiByCompany(searchDto,jobDetailFlag), starRocksExecutor);


        ConcurrentMap<String, RecruitingKpiCommonCountVO> talentMap = talentFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        List<? extends RecruitingKpiCommonCountVO> jobList = jobFuture.join();

        ConcurrentMap<String, RecruitingKpiJobDetailCommonCountVO> jobDetailMap = !jobDetailFlag? new ConcurrentHashMap<>(): jobList.stream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiJobDetailCommonCountVO) a));

        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap = jobList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));


        stopWatch.stop();
        log.info("[apn @{}] searchRecruitingKpiReportByCompany time = {}ms \n {}", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return List.of();
    }


}
